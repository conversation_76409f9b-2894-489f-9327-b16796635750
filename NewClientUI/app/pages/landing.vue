<template>
  <div class="min-h-screen flex flex-col px-4 relative pt-[15vh]">
    <div class="mesh-box" />

    <div class="absolute top-6 right-8 z-20 flex items-center gap-2">
      <UButton
        icon="i-lucide-help-circle"
        variant="ghost"
        size="lg"
        class="text-white/90 hover:text-white"
        to="https://fbeta.de/kontakt/"
        target="_blank"
        external
      >
        Help & Documentation
      </UButton>
      <UserMenu />
    </div>

    <div class="w-full max-w-6xl relative z-10 mx-auto">
      <div class="text-center mb-12">
        <img src="/images/fbeta.png" alt="fbeta" class="md:h-10 mx-auto mb-8">
        <p class="text-xl font-bold text-white/80 mb-12">
          Choose specific domain and start chatting
        </p>
      </div>

      <div v-if="isLoading" class="flex justify-center loading-container">
        <UiLiquidGlassCard class="p-8">
          <div class="flex flex-col items-center space-y-4">
            <UIcon name="i-lucide-loader-2" class="w-8 h-8 text-white/60 animate-spin icon-lg" />
            <p class="text-white/60">
              Loading collections...
            </p>
          </div>
        </UiLiquidGlassCard>
      </div>

      <div v-else-if="error" class="flex justify-center loading-container">
        <UiLiquidGlassCard class="p-8 max-w-md">
          <div class="flex flex-col items-center space-y-4 text-center">
            <UIcon name="i-lucide-alert-circle" class="w-8 h-8 text-red-400 icon-lg" />
            <p class="text-white/80">
              Failed to load collections
            </p>
            <p class="text-sm text-white/60">
              {{ error }}
            </p>
            <UButton
              variant="ghost"
              class="text-white/60 hover:text-white"
              @click="() => window.location.reload()"
            >
              Refresh Page
            </UButton>
          </div>
        </UiLiquidGlassCard>
      </div>

      <div v-else-if="!collections || collections.length === 0" class="flex justify-center loading-container">
        <UiLiquidGlassCard class="p-8 max-w-md">
          <div class="flex flex-col items-center space-y-4 text-center">
            <UIcon name="i-lucide-inbox" class="w-8 h-8 text-white/60 icon-lg" />
            <p class="text-white/80">
              No collections available
            </p>
            <p class="text-sm text-white/60">
              Please contact support if you believe this is an error.
            </p>
          </div>
        </UiLiquidGlassCard>
      </div>

      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div
          v-for="collection in collections"
          :key="collection.id"
          class="cursor-pointer transform transition-all duration-300 hover:scale-105"
          @click="selectDomain(collection)"
        >
          <UiLiquidGlassCard class="h-full">
            <div class="flex flex-col items-center text-center space-y-4 p-4">
              <div>
                <img
                  :src="getDomainIconPath(collection.id)"
                  :alt="collection.name"
                  width="80px"
                >
              </div>

              <div class="space-y-2">
                <UTooltip :text="collection.name" :popper="{ placement: 'right' }">
                  <h3 class="text-xl font-semibold text-white cursor-help">
                    {{ truncateText(collection.name, 18) }}
                  </h3>
                </UTooltip>
                <UTooltip :text="collection.description || ''" :popper="{ placement: 'bottom' }">
                  <p class="text-sm text-white/60 line-clamp-2 cursor-help">
                    {{ truncateText(collection.description || '', 50) }}
                  </p>
                </UTooltip>
              </div>
            </div>
          </UiLiquidGlassCard>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useDomainList } from '~/composables/useDomainList'
import { useAuthStore } from '~/stores/auth'
import { useCollectionsStore, type Collection } from '~/stores/collections'

const { collections, isLoading, error, userInfo } = useDomainList()
const authStore = useAuthStore()
const collectionsStore = useCollectionsStore()
const router = useRouter()

const usedIconIndices = ref(new Set<number>())
const collectionIconMap = ref(new Map<string, string>())

watchEffect(() => {
  if (userInfo.value && userInfo.value.user_name) {
    authStore.updateUserProfile({
      name: userInfo.value.user_name,
      email: userInfo.value.email
    })
  }
})

watchEffect(() => {
  if (collections.value) {
    usedIconIndices.value.clear()
    collectionIconMap.value.clear()
  }
})

function selectDomain(domain: Collection) {
  collectionsStore.selectCollection(domain)
  sessionStorage.setItem('selectedCollection', JSON.stringify(domain))
  router.push('/chat')
}

const domainIcons = [
  'domain-icon-01.png',
  'domain-icon-02.png',
  'domain-icon-03.png',
  'domain-icon-04.png',
  'domain-icon-05.png',
  'domain-icon-06.png',
  'domain-icon-07.png',
  'domain-icon-08.png',
  'domain-icon-09.png',
  'domain-icon-10.png',
  'domain-icon-11.png',
  'domain-icon-12.png',
  'domain-icon-13.png',
  'domain-icon-14.png',
  'domain-icon-15.png',
  'domain-icon-16.png',
  'domain-icon-17.png',
  'domain-icon-18.png',
  'domain-icon-19.png',
  'domain-icon-20.png',
  'domain-icon-21.png',
  'domain-icon-22.png',
  'domain-icon-23.png',
  'domain-icon-24.png',
  'domain-icon-25.png'
]

function truncateText(text: string, maxLength: number): string {
  if (!text) return ''
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength - 3) + '...'
}

function getDomainIconPath(collectionId: string): string {
  let hash = 0
  for (let i = 0; i < collectionId.length; i++) {
    const char = collectionId.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash
  }
  const iconIndex = Math.abs(hash) % domainIcons.length
  return `/images/domainIcons/${domainIcons[iconIndex]}`
}
definePageMeta({
  layout: 'general',
  ssr: false
})
</script>
