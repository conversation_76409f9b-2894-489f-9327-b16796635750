import { defineStore } from 'pinia'

export const useAppStore = defineStore('app', {
  state: () => ({
    isSidebarVisible: true,
    showFavorites: false,
    showUserMenu: false,
    activeModal: null as string | null,
    isLoading: false,
    theme: 'dark' as 'dark' | 'light',
    notifications: [] as Array<{
      id: string
      type: 'success' | 'error' | 'warning' | 'info'
      title: string
      message?: string
      duration?: number
    }>
  }),
  actions: {
    toggleSidebar() {
      this.isSidebarVisible = !this.isSidebarVisible
    },
    toggleFavorites() {
      this.showFavorites = !this.showFavorites
    },
    setLoading(loading: boolean) {
      this.isLoading = loading
    },
    addNotification(notification: Omit<typeof this.notifications[0], 'id'>) {
      const id = Date.now().toString()
      this.notifications.push({ ...notification, id })

      const duration = notification.duration || 5000
      setTimeout(() => {
        this.removeNotification(id)
      }, duration)
    },
    removeNotification(id: string) {
      const index = this.notifications.findIndex(n => n.id === id)
      if (index > -1) {
        this.notifications.splice(index, 1)
      }
    },
    toggleTheme() {
      this.theme = this.theme === 'dark' ? 'light' : 'dark'
      this.applyTheme()
    },
    applyTheme() {
      if (typeof document !== 'undefined') {
        document.documentElement.classList.remove('dark', 'light')
        document.documentElement.classList.add(this.theme)
        localStorage.setItem('theme', this.theme)
      }
    },
    initTheme() {
      if (typeof window !== 'undefined') {
        const savedTheme = localStorage.getItem('theme') as 'dark' | 'light' | null
        if (savedTheme) {
          this.theme = savedTheme
        }
        this.applyTheme()
      }
    }
  }
})
