<template>
  <!-- Show mobile sidebar on small screens -->
  <ChatSidebarMobile v-if="isMobile" />

  <!-- Show desktop sidebar on larger screens -->
  <div v-else-if="appStore.isSidebarVisible" class="chat-sidebar-container">
    <UiLiquidGlassCard
      class="floating-sidebar"
      size="lg"
      rounded
    >
      <div class="sidebar-header">
        <UButton
          icon="i-lucide-plus"
          label="New chat"
          variant="subtle"
          color="neutral"
          size="lg"
          class=" cursor-pointer w-full flex items-center justify-center rounded border "
          :class="appStore.theme === 'light' ? 'text-black-700 hover:text-black-900 border-black-600' : 'text-white/90 hover:text-white'"
          @click="handleNewChat"
        />
      </div>

      <div v-if="showSearch" class="search-container">
        <div class="search-input-wrapper">
          <UInput
            v-model="searchQuery"
            placeholder="Search..."
            class="w-full search-input"
            :ui="{
              base: 'bg-white/10 border-white/20 text-white rounded px-4',
              placeholder: 'placeholder-white/50',
              input: 'text-white px-2',
              icon: { base: 'text-white/60' },
              color: { white: { outline: 'text-white' } }
            }"
            @keyup.esc="closeSearch"
          />
          <button
            v-if="searchQuery"
            type="button"
            class="clear-search-btn"
            @click="clearSearchInput"
          >
            <UIcon name="i-lucide-x" class="w-4 h-4" />
          </button>
        </div>
      </div>

      <div class="sessions-list">
        <div v-if="isSearching && !hasSearchResults" class="empty-state">
          <div class="flex flex-col items-center justify-center py-8">
            <UIcon name="i-lucide-search-x" class="w-12 h-12 text-white/30 mb-3" />
            <p class="text-sm text-white/60 text-center mb-4">
              No results found
            </p>
            <UButton
              label="Clear"
              size="sm"
              variant="soft"
              class="text-white/80 hover:text-white"
              @click="clearSearchInput"
            />
          </div>
        </div>
        <div v-else>
          <!-- Show only Favorites when appStore.showFavorites is true -->
          <div v-if="appStore.showFavorites">
            <div class="section-main-header">
              <h3 class="text-lg font-bold" :class="appStore.theme === 'light' ? 'text-gray-900' : 'text-white'">
                Favorites
              </h3>
              <div class="header-icons">
                <UIcon
                  :name="expandedSections.favoritesOnly ? 'i-lucide-chevron-down' : 'i-lucide-chevron-right'"
                  class="w-5 h-5 cursor-pointer"
                  :class="appStore.theme === 'light' ? 'text-gray-600 hover:text-gray-800' : 'text-white/80 hover:text-white'"
                  @click="toggleSection('favoritesOnly')"
                />
              </div>
            </div>

            <div
              class="session-section drop-zone"
              :class="{ 'drop-active': dragState.dropTargetId === 'favorites' }"
              @dragover="onDragOver($event, 'favorites', 'favorites')"
              @dragenter="onDragEnter"
              @dragleave="onDragLeave"
              @drop="onDrop($event, 'favorites', 'favorites')"
            >
              <div v-show="isSearching ? true : expandedSections.favoritesOnly" class="session-items">
                <div
                  v-for="session in filteredFavorites"
                  :key="session.id"
                  :data-session-id="session.id"
                  class="session-item group draggable"
                  :class="{ active: currentSessionId === session.id }"
                  :draggable="true"
                  @click="selectSession(session.id)"
                  @dragstart="onDragStart($event, session)"
                  @dragend="onDragEnd"
                  @dragover="onDragOverItem($event, session, 'favorites')"
                  @dragleave="onDragLeaveItem"
                  @drop="onDropItem($event, session, 'favorites')"
                >
                  <div class="session-item-content">
                    <UIcon name="i-lucide-star" class="w-4 h-4 text-yellow-400 shrink-0" />
                    <span class="session-title">{{ session.title || 'Untitled Session' }}</span>
                  </div>
                  <SessionDropdown :session="session" @refresh="refreshSessions" />
                </div>
                <div v-if="filteredFavorites.length === 0" class="empty-state">
                  <UIcon name="i-lucide-star-off" class="w-8 h-8 mx-auto mb-2" :class="appStore.theme === 'light' ? 'text-gray-300' : 'text-white/30'" />
                  <p class="text-xs text-center" :class="appStore.theme === 'light' ? 'text-gray-500' : 'text-white/50'">
                    No favorites yet
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- Show everything else when appStore.showFavorites is false -->
          <div v-else>
            <!-- Favorites Section - now at the top -->
            <div class="section-main-header">
              <h3 class="text-lg font-bold" :class="appStore.theme === 'light' ? 'text-gray-900' : 'text-white'">
                Favorites
              </h3>
              <div class="header-icons">
                <UIcon
                  :name="expandedSections.favoritesOnly ? 'i-lucide-chevron-down' : 'i-lucide-chevron-right'"
                  class="w-5 h-5 cursor-pointer"
                  :class="appStore.theme === 'light' ? 'text-gray-600 hover:text-gray-800' : 'text-white/80 hover:text-white'"
                  @click="toggleSection('favoritesOnly')"
                />
              </div>
            </div>

            <div
              class="session-section drop-zone"
              :class="{ 'drop-active': dragState.dropTargetId === 'favorites' }"
              @dragover="onDragOver($event, 'favorites', 'favorites')"
              @dragenter="onDragEnter"
              @dragleave="onDragLeave"
              @drop="onDrop($event, 'favorites', 'favorites')"
            >
              <div v-show="isSearching ? true : expandedSections.favoritesOnly" class="session-items">
                <div
                  v-for="session in filteredFavorites"
                  :key="session.id"
                  :data-session-id="session.id"
                  class="session-item group draggable"
                  :class="{ active: currentSessionId === session.id }"
                  :draggable="true"
                  @click="selectSession(session.id)"
                  @dragstart="onDragStart($event, session)"
                  @dragend="onDragEnd"
                  @dragover="onDragOverItem($event, session, 'favorites')"
                  @dragleave="onDragLeaveItem"
                  @drop="onDropItem($event, session, 'favorites')"
                >
                  <div class="session-item-content">
                    <UIcon name="i-lucide-star" class="w-4 h-4 text-yellow-400 shrink-0" />
                    <span class="session-title">{{ session.title || 'Untitled Session' }}</span>
                  </div>
                  <SessionDropdown :session="session" @refresh="refreshSessions" />
                </div>
                <div v-if="filteredFavorites.length === 0" class="empty-state">
                  <UIcon name="i-lucide-star-off" class="w-8 h-8 mx-auto mb-2" :class="appStore.theme === 'light' ? 'text-gray-300' : 'text-white/30'" />
                  <p class="text-xs text-center" :class="appStore.theme === 'light' ? 'text-gray-500' : 'text-white/50'">
                    No favorites yet
                  </p>
                </div>
              </div>
            </div>

            <!-- Recents Section -->
            <div class="section-main-header">
              <h3 class="text-lg font-bold" :class="appStore.theme === 'light' ? 'text-gray-900' : 'text-white'">
                Recents
              </h3>
              <div class="header-icons">
                <UIcon
                  name="i-lucide-search"
                  class="w-5 h-5 cursor-pointer"
                  :class="appStore.theme === 'light' ? 'text-gray-600 hover:text-gray-800' : 'text-white/80 hover:text-white'"
                  @click="toggleSearch"
                />

                <UIcon
                  name="i-lucide-plus"
                  class="w-5 h-5 cursor-pointer"
                  :class="appStore.theme === 'light' ? 'text-gray-600 hover:text-gray-800' : 'text-white/80 hover:text-white'"
                  @click.stop="handleCreateFolder"
                />
                <UIcon
                  :name="allSectionsExpanded ? 'i-lucide-chevron-up' : 'i-lucide-chevron-down'"
                  class="w-5 h-5 cursor-pointer"
                  :class="appStore.theme === 'light' ? 'text-gray-600 hover:text-gray-800' : 'text-white/80 hover:text-white'"
                  @click="toggleAllSections"
                />
              </div>
            </div>

            <div v-if="isLoadingSessions" class="loading-container">
              <div class="flex flex-col items-center justify-center py-8">
                <UIcon name="i-lucide-loader-2" class="w-8 h-8 text-white/40 animate-spin mb-3" />
                <p class="text-sm text-white/50">
                  Loading sessions...
                </p>
              </div>
            </div>
            <div v-else-if="!hasAnySessions" class="empty-state">
              <div class="flex flex-col items-center justify-center py-8">
                <UIcon name="i-lucide-inbox" class="w-12 h-12 text-white/30 mb-3" />
                <p class="text-sm text-white/50 text-center mb-3">
                  No sessions yet
                </p>
                <p class="text-xs text-white/40 text-center mb-4">
                  Start a new chat to create your first session
                </p>
                <UButton
                  icon="i-lucide-plus"
                  label="New chat"
                  variant="soft"
                  size="sm"
                  class="text-white/80 hover:text-white"
                  @click="handleNewChat"
                />
              </div>
            </div>

            <div v-else>
              <div
                v-for="(sessions, folderName) in (isSearching ? filteredFolderSessions : folderSessions)"
                :key="folderName"
                class="session-section drop-zone folder-section"
                :class="{ 'drop-active': dragState.dropTargetId === folderName }"
                @dragover="onDragOver($event, folderName, 'folder')"
                @dragenter="onDragEnter"
                @dragleave="onDragLeave"
                @drop="onDrop($event, folderName, 'folder')"
              >
                <div class="section-header" @click="toggleSection(folderName)">
                  <div class="flex items-center gap-2">
                    <UIcon
                      :name="expandedSections[folderName] ? 'i-lucide-chevron-down' : 'i-lucide-chevron-right'"
                      class="w-4 h-4"
                      :class="appStore.theme === 'light' ? 'text-gray-500' : 'text-white/60'"
                    />
                    <UIcon name="i-lucide-folder" class="w-4 h-4" :class="appStore.theme === 'light' ? 'text-gray-600' : 'text-white/70'" />
                    <h3 class="text-sm font-semibold" :class="appStore.theme === 'light' ? 'text-gray-800' : 'text-white/90'" :title="folderName">
                      {{ truncateName(folderName) }}
                    </h3>
                  </div>
                  <div class="flex items-center gap-2" @click.stop>
                    <span class="text-xs folder-count" :class="appStore.theme === 'light' ? '' : 'text-white/50'">{{ sessions.length }}</span>
                    <FolderDropdown :folder-name="folderName" @refresh="refreshSessions" />
                  </div>
                </div>
                <div v-show="isSearching ? true : expandedSections[folderName]" class="session-items">
                  <div
                    v-for="session in sessions"
                    :key="session.id"
                    :data-session-id="session.id"
                    class="session-item group draggable"
                    :class="{ active: currentSessionId === session.id }"
                    :draggable="true"
                    @click="selectSession(session.id)"
                    @dragstart="onDragStart($event, session)"
                    @dragend="onDragEnd"
                    @dragover="onDragOverItem($event, session, folderName)"
                    @dragleave="onDragLeaveItem"
                    @drop="onDropItem($event, session, folderName)"
                  >
                    <div class="session-item-content">
                      <UIcon name="i-lucide-message-circle" class="w-4 h-4 shrink-0" :class="appStore.theme === 'light' ? 'text-gray-500' : 'text-white/60'" />
                      <span class="session-title">{{ session.title || 'Unbenannte Sitzung' }}</span>
                    </div>
                    <SessionDropdown :session="session" @refresh="refreshSessions" />
                  </div>
                </div>
              </div>

              <div
                v-if="unassignedSessions.length > 0 || dragState.isDragging"
                class="session-section my-6 drop-zone"
                :class="{ 'drop-active': dragState.dropTargetId === 'unassigned' }"
                @dragover="onDragOver($event, 'unassigned', 'unassigned')"
                @dragenter="onDragEnter"
                @dragleave="onDragLeave"
                @drop="onDrop($event, 'unassigned', 'unassigned')"
              >
                <div class="section-header" @click="toggleSection('unassigned')">
                  <div class="flex items-center gap-2">
                    <UIcon
                      :name="expandedSections.unassigned ? 'i-lucide-chevron-down' : 'i-lucide-chevron-right'"
                      class="w-4 h-4"
                      :class="appStore.theme === 'light' ? 'text-gray-500' : 'text-white/60'"
                    />
                    <h3 class="text-sm font-semibold" :class="appStore.theme === 'light' ? 'text-gray-800' : 'text-white/90'">
                      Aktuelles
                    </h3>
                  </div>
                  <div class="flex items-center gap-2">
                    <span class="text-xs folder-count" :class="appStore.theme === 'light' ? '' : 'text-white/50'">{{ unassignedSessions.length }}</span>
                    <UIcon
                      name="i-lucide-plus"
                      class="w-4 h-4 cursor-pointer"
                      :class="appStore.theme === 'light' ? 'text-gray-500 hover:text-gray-700' : 'text-white/60 hover:text-white'"
                      @click.stop="handleCreateFolder"
                    />
                  </div>
                </div>
                <div v-show="isSearching ? true : expandedSections.unassigned" class="session-items">
                  <div v-if="dragState.isDragging && unassignedSessions.length === 0" class="empty-drop-zone">
                    <UIcon name="i-lucide-folder-plus" class="w-8 h-8 text-white/30 mx-auto mb-2" />
                    <p class="text-xs text-white/50 text-center">
                      Drop here to move to unassigned
                    </p>
                  </div>
                  <div
                    v-for="session in (isSearching ? filteredUnassignedSessions : unassignedSessions)"
                    :key="session.id"
                    :data-session-id="session.id"
                    class="session-item group draggable"
                    :class="{ active: currentSessionId === session.id }"
                    :draggable="true"
                    @click="selectSession(session.id)"
                    @dragstart="onDragStart($event, session)"
                    @dragend="onDragEnd"
                    @dragover="onDragOverItem($event, session, 'unassigned')"
                    @dragleave="onDragLeaveItem"
                    @drop="onDropItem($event, session, 'unassigned')"
                  >
                    <div class="session-item-content">
                      <UIcon name="i-lucide-message-circle" class="w-4 h-4 shrink-0" :class="appStore.theme === 'light' ? 'text-gray-500' : 'text-white/60'" />
                      <span class="session-title">{{ session.title || 'Unbenannte Sitzung' }}</span>
                    </div>
                    <SessionDropdown :session="session" @refresh="refreshSessions" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Controls positioned at the bottom of the sidebar -->
      <div class="sidebar-controls-left">
        <UiLiquidGlassCard
          size="sm"
          class="cursor-pointer theme-toggle-btn"
          rounded
          :opacity="0"
          @click="appStore.toggleTheme"
        >
          <UButton
            :icon="appStore.theme === 'dark' ? 'i-lucide-sun' : 'i-lucide-moon'"
            variant="link"
            active-variant="link"
            size="lg"
            class="cursor-pointer control-btn"
            :class="appStore.theme === 'light' ? 'text-gray-700 hover:text-gray-900' : 'text-white hover:text-white'"
          />
        </UiLiquidGlassCard>
      </div>
      <div class="sidebar-controls">
        <UiLiquidGlassCard
          size="sm"
          class="cursor-pointer"
          rounded
          :opacity="0"
          @click="appStore.toggleSidebar"
        >
          <UButton
            icon="i-lucide-x"
            variant="link"
            active-variant="link"
            size="lg"
            class="cursor-pointer control-btn"
            :class="appStore.theme === 'light' ? 'text-gray-700 hover:text-gray-900' : 'text-white hover:text-white'"
          />
        </UiLiquidGlassCard>
      </div>
    </UiLiquidGlassCard>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from '~/stores/app'
import { useSidebar } from '~/composables/useSidebar'
import { useDragDrop } from '~/composables/useDragDrop'
import SessionDropdown from './SessionDropdown.vue'
import FolderDropdown from './FolderDropdown.vue'
import ChatSidebarMobile from './ChatSidebarMobile.vue'

// Detect mobile screen size
const isMobile = ref(false)

const updateScreenSize = () => {
  if (import.meta.client) {
    isMobile.value = window.innerWidth <= 1024
  }
}

onMounted(() => {
  updateScreenSize()
  if (import.meta.client) {
    window.addEventListener('resize', updateScreenSize)
  }
})

onUnmounted(() => {
  if (import.meta.client) {
    window.removeEventListener('resize', updateScreenSize)
  }
})

const appStore = useAppStore()

const {
  dragState,
  onDragStart,
  onDragOver,
  onDragEnter,
  onDragLeave,
  onDrop,
  onDragEnd,
  onDragOverItem,
  onDragLeaveItem,
  onDropItem
} = useDragDrop()

onMounted(() => {
  appStore.initTheme()
})

const {
  searchQuery,
  showSearch,
  expandedSections,
  favoriteSessions: _favoriteSessions,
  unassignedSessions,
  folderSessions,
  currentSessionId,
  isLoadingSessions,
  hasAnySessions,
  filteredFavorites,
  allSectionsExpanded,
  truncateName,
  toggleSection,
  toggleAllSections,
  selectSession,
  handleNewChat,
  handleCreateFolder,
  refreshSessions,
  toggleSearch,
  closeSearch,
  isSearching,
  filteredFolderSessions,
  filteredUnassignedSessions,
  hasSearchResults,
  clearSearchInput
} = useSidebar()
</script>

<style scoped>
.chat-sidebar-container {
  position: relative;
  z-index: 10;
}

.floating-sidebar {
  position: fixed;
  left: 2rem;
  top: 2rem;
  bottom: 2rem;
  width: 320px;
  transition: all 0.3s ease-in-out;
  z-index: 15;
  display: flex;
  flex-direction: column;
  overflow: auto;
}

.sidebar-header {
  margin-bottom: 1rem;
}

.search-container {
  margin-bottom: 1.5rem;
}

.search-input-wrapper {
  position: relative;
}

.clear-search-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  padding: 4px;
  border: none;
  border-radius: 6px;
  background: transparent;
  color: rgba(255,255,255,0.7);
  cursor: pointer;
}

.clear-search-btn:hover {
  background: rgba(255,255,255,0.1);
  color: #fff;
}

html.light .clear-search-btn {
  color: var(--brand-primary);
}

html.light .clear-search-btn:hover {
  background: rgba(0,0,0,0.06);
}

html.dark .search-input :deep(input) {
  color: white !important;
}

html.dark .search-input :deep(input::placeholder) {
  color: rgba(255, 255, 255, 0.5) !important;
}

html.light .search-input :deep(input) {
  color: var(--text-primary) !important;
}

html.light .search-input :deep(input::placeholder) {
  color: var(--text-muted) !important;
}

.sessions-list {
  flex: 1;
  overflow-y: auto;
  overflow-x: auto;
  padding-right: 0.5rem;
  margin-right: -0.5rem;
  margin-bottom: 25px
}
.sessions-list::-webkit-scrollbar {
  width: 6px;
}

.sessions-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
}

.sessions-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
}

.sessions-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

.session-section {
  margin-bottom: 0.2rem;
}

.section-main-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
}

.header-icons {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 0.75rem;
  margin-bottom: 0.15rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: background-color 0.2s;
  user-select: none;
}

.section-header:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.session-items {
  margin-bottom: 0.5rem;
}

.session-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 0.75rem;
  margin-bottom: 0.125rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

/* Indent items within folder sections when open */
.folder-section .session-items .session-item {
  padding-left: 1.75rem;
}

.session-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.session-item.active {
  background-color: rgba(255, 255, 255, 0.15);
  border-left: 2px solid rgba(255, 255, 255, 0.5);
}

.session-item-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
  min-width: 0;
}

.session-title {
  flex: 1;
  font-size: 0.875rem;
  truncate: true;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

html.dark .session-title {
  color: rgba(255, 255, 255, 0.9);
}

html.light .session-title {
  color: var(--text-primary);
}

/* Light mode: folder counts use brand primary for visibility */
html.light .folder-count {
  color: var(--brand-primary) !important;
}

.empty-state {
  padding: 2rem 1rem;
  text-align: center;
}

.loading-container {
  padding: 2rem 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-controls-left {
  position: absolute;
  bottom: 0rem;
  left: 0rem;
  display: flex;
  gap: 0.5rem;
}

.sidebar-controls {
  position: absolute;
  bottom: 0rem;
  right: 0rem;
  display: flex;
  gap: 0.5rem;
}

.clickable-card {
  cursor: pointer;
  transition: all 0.2s ease;
}

.clickable-card:hover {
  transform: scale(1.05);
}

.delete-card {
  padding: 0.25rem;
  min-width: auto;
}

.show-sidebar-btn {
  position: fixed;
  top: 2rem;
  left: 2rem;
  z-index: 50;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.show-sidebar-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.draggable {
  cursor: pointer;
}

.draggable:hover {
  transform: translateX(2px);
}

.draggable.dragging {
  opacity: 0.5;
  transform: scale(0.98);
  cursor: pointer;
}

.drop-zone {
  position: relative;
  transition: all 0.3s ease;
}

.drop-zone.drag-over {
  background: rgba(59, 130, 246, 0.05);
  border-radius: 0.5rem;
}

.drop-zone.drag-over::before {
  content: '';
  position: absolute;
  inset: 0;
  border: 2px dashed rgba(59, 130, 246, 0.5);
  border-radius: 0.5rem;
  pointer-events: none;
  animation: pulse-border 2s infinite;
}

.drop-zone.drag-enter {
  transform: scale(1.02);
}

.drop-zone.drop-active {
  background: rgba(106, 8, 30, 0.08);
}

.drop-zone.drop-active::after {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(34, 197, 94, 0.1) 50%,
    transparent 70%
  );
  animation: shimmer 2s infinite;
  pointer-events: none;
  border-radius: 0.5rem;
}

.empty-drop-zone {
  padding: 2rem 1rem;
  margin: 0.5rem;
  border: 2px dashed rgba(255, 255, 255, 0.2);
  border-radius: 0.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

.drop-zone.drag-over .empty-drop-zone {
  border-color: rgba(59, 130, 246, 0.5);
  background: rgba(59, 130, 246, 0.05);
}

@keyframes pulse-border {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(200%);
  }
}

.drop-zone[data-drop-type="favorites"].drag-over {
  background: rgba(250, 204, 21, 0.05);
}

.drop-zone[data-drop-type="favorites"].drag-over::before {
  border-color: rgba(250, 204, 21, 0.5);
}

.drop-zone[data-drop-type="unassigned"].drag-over {
  background: rgba(156, 163, 175, 0.05);
}

.drop-zone[data-drop-type="unassigned"].drag-over::before {
  border-color: rgba(156, 163, 175, 0.5);
}

.session-item.drag-over-top::before {
  content: '';
  position: absolute;
  top: -2px;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(59, 130, 246, 0.8) 20%,
    rgba(59, 130, 246, 0.8) 80%,
    transparent
  );
  border-radius: 2px;
  animation: pulse-line 1.5s infinite;
  pointer-events: none;
  z-index: 10;
}

.session-item.drag-over-bottom::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(59, 130, 246, 0.8) 20%,
    rgba(59, 130, 246, 0.8) 80%,
    transparent
  );
  border-radius: 2px;
  animation: pulse-line 1.5s infinite;
  pointer-events: none;
  z-index: 10;
}

@keyframes pulse-line {
  0%, 100% {
    opacity: 0.6;
    transform: scaleX(0.95);
  }
  50% {
    opacity: 1;
    transform: scaleX(1);
  }
}

.draggable.dragging {
  opacity: 0.4;
  transform: scale(0.98);
  cursor: pointer;
  background: rgba(255, 255, 255, 0.05);
}

.session-items {
  position: relative;
}

.session-item {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              opacity 0.2s ease,
              background-color 0.2s ease;
}

.session-item.reorder-target {
  transform: translateY(2px);
}

.draggable {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.draggable:active {
  cursor: pointer;
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateX(-0.25rem);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Use existing theme system variables for better consistency */
.floating-sidebar {
  background: var(--glass-bg) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid var(--glass-border) !important;
}

.session-item:hover {
  background-color: var(--hover-bg);
}

.session-item.active {
  background-color: var(--hover-bg);
  border-left: 2px solid var(--brand-primary);
}

.section-header:hover {
  background-color: var(--hover-bg);
}

.sessions-list::-webkit-scrollbar-track {
  background: var(--scrollbar-bg);
}

.sessions-list::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
}

.sessions-list::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover);
}

@media (max-width: 1024px) {
  .floating-sidebar {
    display: none;
  }
}

@media (max-width: 768px) {
  .floating-sidebar {
    left: 1rem;
    width: 250px;
  }

  .show-sidebar-btn {
    left: 1rem;
  }
}
</style>
