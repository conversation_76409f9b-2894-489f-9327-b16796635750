<template>
  <div class="chat-input-container">
    <UiLiquidGlassCard class="chat-input-card" size="sm">
      <form class="chat-form" @submit.prevent="handleSubmit">
        <div class="input-row">
          <!-- Collection Selector -->
          <CollectionSelector
            v-model="selectedCollectionId"
            @change="selectCollection"
          />

          <!-- Attachments -->
          <input
            ref="fileInputRef"
            type="file"
            class="hidden"
            accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt,.rtf,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/plain,application/rtf"
            @change="onFileSelected"
          >
          <UButton
            type="button"
            icon="i-lucide-paperclip"
            variant="ghost"
            class="send-btn text-secondary-custom"
            :loading="loading"
            @click="openFilePicker"
          />

          <!-- Input field -->
          <input
            v-model="inputValue"
            type="text"
            placeholder="Ask a question..."
            class="chat-input"
            :disabled="loading"
          >

          <!-- Send button -->
          <UButton
            type="button"
            :icon="recognizing ? 'i-lucide-square' : 'i-lucide-mic'"
            variant="ghost"
            class="send-btn text-secondary-custom mx-2"
            @click="startSpeechToText"
          />
        </div>
      </form>
    </UiLiquidGlassCard>
  </div>
</template>

<script setup lang="ts">
import { useCollectionsStore } from '~/stores/collections'
import { useRouter } from 'vue-router'
import CollectionSelector from './CollectionSelector.vue'
import { useAppStore } from '~/stores/app'

interface Props {
  loading?: boolean
}

interface Emits {
  (e: 'submit', message: string): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()
const collectionsStore = useCollectionsStore()
const router = useRouter()
const appStore = useAppStore()

const inputValue = ref('')
const fileInputRef = ref<HTMLInputElement | null>(null)
const selectedFiles = ref<File[] | null>(null)

const selectedCollectionId = computed({
  get: () => collectionsStore.currentCollection?.id || '',
  set: (value: string) => {
    const collection = collectionsStore.collections.find(c => c.id === value)
    if (collection) {
      selectCollection(collection)
    }
  }
})

const selectCollection = async (collection: typeof collectionsStore.collections[0]) => {
  collectionsStore.selectCollection(collection)
  sessionStorage.setItem('selectedCollection', JSON.stringify(collection))
  router.go(0)
}

const handleSubmit = () => {
  if (!inputValue.value.trim() || props.loading) return

  emit('submit', inputValue.value.trim())
  inputValue.value = ''
}

const openFilePicker = () => {
  if (props.loading) return
  fileInputRef.value?.click()
}

const onFileSelected = (e: Event) => {
  const input = e.target as HTMLInputElement
  if (!input.files || input.files.length === 0) return
  selectedFiles.value = Array.from(input.files)
  appStore.addNotification({
    type: 'info',
    title: selectedFiles.value.length === 1 ? '1 file selected' : `${selectedFiles.value.length} files selected`
  })
}

let recognizing = false
let recognition: any = null
let recognitionAttempts = 0
const maxRecognitionAttempts = 2

if (typeof window !== 'undefined') {
  const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition
  if (SpeechRecognition) {
    recognition = new SpeechRecognition()
    recognition.continuous = false
    recognition.interimResults = false
    recognition.lang = 'en-US'

    recognition.onresult = (event: any) => {
      const transcript = Array.from(event.results)
        .map((result: any) => result[0])
        .map((res: any) => res.transcript)
        .join(' ')
      inputValue.value = transcript
    }

    recognition.onerror = (event: any) => {
      recognizing = false
      if (event?.error === 'network' && recognitionAttempts < maxRecognitionAttempts) {
        appStore.addNotification({ type: 'info', title: 'Retrying speech recognition…' })
        setTimeout(() => {
          try {
            recognition.start()
            recognizing = true
            recognitionAttempts += 1
          } catch (e) {
            recognizing = false
          }
        }, 600)
        return
      }
      if (event?.error === 'network' && recognitionAttempts >= maxRecognitionAttempts) {
        appStore.addNotification({ type: 'error', title: `Speech recognition failed after ${maxRecognitionAttempts} attempts` })
        return
      }
      appStore.addNotification({ type: 'error', title: 'Speech recognition error', message: String(event?.error || '') })
    }

    recognition.onend = () => {
      recognizing = false
      recognitionAttempts = 0
    }

    recognition.onspeechend = () => {
      try { recognition.stop() } catch {}
    }
  }
}

const startSpeechToText = () => {
  return false; //Temp disable speech to text
  if (props.loading) return
  if (!recognition) {
    appStore.addNotification({ type: 'warning', title: 'Speech to text not supported', message: 'Your browser does not support Web Speech API.' })
    return
  }
  // Web Speech often requires secure context except for localhost
  if (!(window.isSecureContext || location.hostname === 'localhost')) {
    appStore.addNotification({ type: 'warning', title: 'Use HTTPS for speech', message: 'Speech recognition requires a secure context (https) in most browsers.' })
    return
  }
  if (!recognizing) {
    recognizing = true
    try {
      recognitionAttempts = 1
      recognition.start()
      appStore.addNotification({ type: 'info', title: 'Listening…' })
    } catch (e) {
      recognizing = false
      recognitionAttempts = 0
    }
  } else {
    try {
      recognition.stop()
    } catch {}
    recognizing = false
  }
}

onMounted(async () => {
  if (!collectionsStore.hasCollections) {
    await collectionsStore.fetchCollections()
  }
})
</script>

<style scoped>
.chat-input-container {
  width: 100%;
  z-index: 10;
}
.chat-input-card {
  width: 100%;
  overflow: visible !important;
}

.chat-form {
  width: 100%;
  overflow: visible !important;
}

/* Ensure the glass card doesn't clip the dropdown */
:deep(.ui-liquid-glass-card) {
  overflow: visible !important;
}

.input-row {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
}

.chat-input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  color: white;
  font-size: 0.875rem;
  placeholder-color: rgba(255, 255, 255, 0.5);
}

.chat-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.chat-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.send-btn {
  flex-shrink: 0;
  cursor: pointer;
  transition: transform 0.15s ease, background-color 0.15s ease;
}

.send-btn:hover:not(:disabled) {
  background: rgba(173, 58, 79, 0.25) !important; /* brand hover tint */
  transform: scale(1.05);
}

.send-btn:active:not(:disabled) {
  transform: scale(0.98);
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .input-row {
    gap: 0.5rem;
  }
}

/* Ensure input works on mobile */
@media (max-width: 480px) {
  .collection-select {
    display: none;
  }
}
</style>
