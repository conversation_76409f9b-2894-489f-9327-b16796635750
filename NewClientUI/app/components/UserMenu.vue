<script setup lang="ts">
import { useAppStore } from '~/stores/app'
import { useAuthStore } from '~/stores/auth'

interface Props {
  collapsed?: boolean
}

defineProps<Props>()

const { user: _user, logout, isLoading } = useAuth()
const appStore = useAppStore()
const authStore = useAuthStore()

// Computed properties for auth store getters
const userAvatar = computed(() => authStore.userAvatar)
const userName = computed(() => authStore.userName || 'User')
const userEmail = computed(() => authStore.userEmail || localStorage.getItem('user_email') || '')

const open = ref(false)
const userMenuStyle = ref({})

function handleSettingsClick() {
  open.value = false
  window.open('https://rag.dev.fbeta.tech/admin/', '_blank')
}

async function handleLogoutClick() {
  open.value = false
  try {
    await logout()
    appStore.addNotification({
      type: 'success',
      title: 'Logged out successfully'
    })
  } catch (error) {
    console.error('Logout error:', error)
    appStore.addNotification({
      type: 'error',
      title: 'Logout failed',
      message: 'Please try again'
    })
  }
}

const toggleUserMenu = () => {
  if (open.value) {
    closeUserMenu()
  } else {
    openUserMenu()
  }
}

const openUserMenu = () => {
  open.value = true
  nextTick(() => updateUserMenuPosition())
}

const closeUserMenu = () => {
  open.value = false
}

const updateUserMenuPosition = () => {
  const trigger = document.querySelector('.user-menu-trigger')
  if (!trigger) return

  const rect = trigger.getBoundingClientRect()
  const dropdownWidth = 250
  const dropdownHeight = 200
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight
  const spacing = 8

  // Start with bottom-left positioning
  let left = rect.left
  let top = rect.bottom + spacing

  // If dropdown would overflow right edge, adjust left
  if (left + dropdownWidth > viewportWidth - 10) {
    left = viewportWidth - dropdownWidth - 10
  }

  // If dropdown would overflow bottom edge, position above
  if (top + dropdownHeight > viewportHeight - 10) {
    top = rect.top - dropdownHeight - spacing
  }

  // Ensure dropdown doesn't go above viewport
  if (top < 10) {
    top = 10
  }

  // Ensure dropdown doesn't go left of viewport
  if (left < 10) {
    left = 10
  }

  userMenuStyle.value = {
    position: 'fixed',
    left: `${left}px`,
    top: `${top}px`,
    zIndex: 9999
  }
}
</script>

<template>
  <div>
    <UiLiquidGlassCard
      size="sm"
      class="cursor-pointer user-menu-trigger"
      rounded
      :opacity="0"
      @click="toggleUserMenu"
    >
      <UButton
        icon="i-lucide-user"
        variant="link"
        active-variant="link"
        size="lg"
        class="cursor-pointer control-btn text-secondary-custom"
      />
    </UiLiquidGlassCard>

    <Teleport to="body">
      <Transition
        enter-active-class="transition ease-out duration-100"
        enter-from-class="transform opacity-0 scale-95"
        enter-to-class="transform opacity-100 scale-100"
        leave-active-class="transition ease-in duration-75"
        leave-from-class="transform opacity-100 scale-100"
        leave-to-class="transform opacity-0 scale-95"
      >
        <div v-if="open" class="user-menu-overlay" @click="closeUserMenu">
          <div class="user-menu-wrapper" :style="userMenuStyle" @click.stop>
            <UiLiquidGlassCard class="user-menu-content" size="sm" rounded>
              <div class="p-4 space-y-4">
                <!-- User Info Header -->
                <div class="flex items-center gap-3 pb-3 border-b border-white/20">
                  <div class="w-10 h-10 rounded-full flex items-center justify-center shadow-lg border border-white/20">
                    <img
                      v-if="userAvatar"
                      :src="userAvatar"
                      :alt="userName"
                      class="w-full h-full rounded-full object-cover"
                    >
                    <span v-else class="text-white font-semibold text-sm">
                      {{ userName.charAt(0).toUpperCase() }}
                    </span>
                  </div>
                  <div class="flex-1 min-w-0">
                    <p class="text-sm font-semibold text-white truncate">
                      {{ userName }}
                    </p>
                    <p class="text-xs text-gray-400 truncate">
                      {{ userEmail || 'No email available' }}
                    </p>
                  </div>
                  <UButton
                    color="neutral"
                    variant="ghost"
                    icon="i-lucide-x"
                    size="xs"
                    class="text-gray-400 hover:text-white transition-colors"
                    @click="closeUserMenu"
                  />
                </div>

                <!-- Menu Actions -->
                <div class="space-y-1">
                  <div class="pt-2 mt-2">
                    <button
                      class="user-menu-item text-white/90 hover:text-white hover:bg-white/10"
                      @click="handleSettingsClick"
                    >
                      <UIcon name="i-lucide-settings" class="w-4 h-4" />
                      <span>Settings</span>
                    </button>
                  </div>
                  <div class="pt-2 mt-2">
                    <button
                      class="user-menu-item text-red-400 hover:text-red-300 hover:bg-red-500/20"
                      @click="handleLogoutClick"
                    >
                      <UIcon name="i-lucide-log-out" class="w-4 h-4" />
                      <span>Logout</span>
                    </button>
                  </div>
                </div>
              </div>
            </UiLiquidGlassCard>
          </div>
        </div>
      </Transition>
    </Teleport>
  </div>
</template>

<style scoped>
/* User menu overlay */
.user-menu-overlay {
  position: fixed;
  inset: 0;
  z-index: 9998;
}

/* Light mode: user menu item text should be black */
html.light .user-menu-item {
  color: #000000;
}
html.light .user-menu-item:hover {
  background-color: var(--hover-bg);
  color: #000000;
}

.user-menu-wrapper {
  position: fixed;
  filter: drop-shadow(0 10px 15px rgba(0,0,0,0.3));
}

.user-menu-content {
  min-width: 250px;
}

.user-menu-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.9);
  text-align: left;
  border-radius: 0.375rem;
  transition: all 0.15s;
  background-color: transparent;
  border: none;
  cursor: pointer;
}

.user-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}
</style>
