<script setup lang="ts">
const colorMode = useColorMode()
const authStore = useAuthStore()

const color = computed(() => colorMode.value === 'dark' ? '#1b1718' : 'white')

useHead({
  meta: [
    { charset: 'utf-8' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1' },
    { key: 'theme-color', name: 'theme-color', content: color }
  ],
  link: [
    { rel: 'icon', href: '/favicon.ico' },
    { rel: 'stylesheet', href: 'https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap' }
  ],
  script: [
    {
      innerHTML: `
        (function() {
          const theme = localStorage.getItem('theme') || 'dark';
          document.documentElement.classList.add(theme);
        })()
      `,
      type: 'text/javascript'
    }
  ],
  htmlAttrs: {
    lang: 'en'
  }
})

const title = 'fbeta - AI Chat Platform'
const description = 'Intelligent AI chat platform with domain-specific expertise.'

useSeoMeta({
  title,
  description,
  ogTitle: title,
  ogDescription: description
})
</script>

<template>
  <UApp :toaster="{ position: 'top-right' }">
    <AuthLoader v-if="authStore.isLoading" />

    <template v-else>
      <NuxtLoadingIndicator color="var(--ui-primary)" />

      <NuxtLayout>
        <NuxtPage />
      </NuxtLayout>

      <NotificationSystem />
      <DialogProvider />
    </template>
  </UApp>
</template>
