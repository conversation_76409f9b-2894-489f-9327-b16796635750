import type { Session } from '~/stores/collections'
import { useCollectionsStore } from '~/stores/collections'
import { useAppStore } from '~/stores/app'

export interface DragDropState {
  isDragging: boolean
  draggedSession: Session | null
  draggedFromFolder: string | null
  dropTargetId: string | null
  dropTargetType: 'folder' | 'favorites' | 'unassigned' | null
  dropIndex: number | null
  isReordering: boolean
}

export const useDragDrop = () => {
  const collectionsStore = useCollectionsStore()
  const appStore = useAppStore()

  const dragState = reactive<DragDropState>({
    isDragging: false,
    draggedSession: null,
    draggedFromFolder: null,
    dropTargetId: null,
    dropTargetType: null,
    dropIndex: null,
    isReordering: false
  })

  const findSessionFolder = (sessionId: string): string | null => {
    if (!collectionsStore.sessions.folders) return null

    for (const [folderName, sessions] of Object.entries(collectionsStore.sessions.folders)) {
      if (Array.isArray(sessions) && sessions.some(s => s.id === sessionId)) {
        if (folderName === 'favorites' || folderName === 'unassigned') {
          return folderName
        }
        return folderName
      }
    }
    return null
  }

  const onDragStart = (event: DragEvent, session: Session) => {
    if (!event.dataTransfer) return

    dragState.isDragging = true
    dragState.draggedSession = session
    dragState.draggedFromFolder = findSessionFolder(session.id)

    event.dataTransfer.effectAllowed = 'move'
    event.dataTransfer.setData('text/plain', session.id)

    const target = event.target as HTMLElement
    target.classList.add('dragging')

    const dragImage = target.cloneNode(true) as HTMLElement
    dragImage.style.position = 'absolute'
    dragImage.style.top = '-1000px'
    dragImage.style.opacity = '0.8'
    dragImage.style.transform = 'rotate(2deg)'
    document.body.appendChild(dragImage)
    event.dataTransfer.setDragImage(dragImage, event.offsetX, event.offsetY)

    setTimeout(() => {
      document.body.removeChild(dragImage)
    }, 0)
  }

  const onDragOver = (event: DragEvent, targetId: string, targetType: 'folder' | 'favorites' | 'unassigned') => {
    event.preventDefault()

    if (!dragState.isDragging || !dragState.draggedSession) return

    dragState.isReordering = dragState.draggedFromFolder === targetId

    event.dataTransfer!.dropEffect = 'move'
    dragState.dropTargetId = targetId
    dragState.dropTargetType = targetType

    const target = event.currentTarget as HTMLElement
    if (!dragState.isReordering) {
      target.classList.add('drag-over')
    }
  }

  const onDragEnter = (event: DragEvent) => {
    event.preventDefault()
    const target = event.currentTarget as HTMLElement
    target.classList.add('drag-enter')
  }

  const onDragLeave = (event: DragEvent) => {
    const target = event.currentTarget as HTMLElement
    target.classList.remove('drag-over', 'drag-enter')

    const relatedTarget = event.relatedTarget as HTMLElement
    if (target.contains(relatedTarget)) return

    dragState.dropTargetId = null
    dragState.dropTargetType = null
  }

  const onDrop = async (event: DragEvent, targetId: string, targetType: 'folder' | 'favorites' | 'unassigned', dropIndex?: number) => {
    event.preventDefault()
    event.stopPropagation()

    const target = event.currentTarget as HTMLElement
    target.classList.remove('drag-over', 'drag-enter')

    if (!dragState.draggedSession || !dragState.isDragging) return

    const sessionId = dragState.draggedSession.id
    const fromFolder = dragState.draggedFromFolder

    if (fromFolder === targetId && dropIndex !== undefined) {
      try {
        collectionsStore.moveSessionWithinFolder(targetId, sessionId, dropIndex)
        appStore.addNotification({
          type: 'success',
          title: 'Session reordered'
        })
      } catch (error) {
        console.error('Error reordering session:', error)
        appStore.addNotification({
          type: 'error',
          title: 'Failed to reorder session'
        })
      }
      resetDragState()
      return
    }

    try {
      if (targetType === 'favorites') {
        await collectionsStore.updateFavoriteStatus(sessionId, true)
        appStore.addNotification({
          type: 'success',
          title: 'Session added to favorites'
        })
      } else if (targetType === 'unassigned') {
        if (fromFolder === 'favorites') {
          await collectionsStore.updateFavoriteStatus(sessionId, false)
          appStore.addNotification({
            type: 'success',
            title: 'Session removed from favorites'
          })
        } else if (fromFolder && fromFolder !== 'unassigned') {
          await collectionsStore.removeSessionFromFolder(sessionId, fromFolder)
          appStore.addNotification({
            type: 'success',
            title: 'Session moved to unassigned'
          })
        }
      } else if (targetType === 'folder' && targetId) {
        if (fromFolder === 'favorites') {
          await collectionsStore.updateFavoriteStatus(sessionId, false)
        } else if (fromFolder && fromFolder !== 'unassigned' && fromFolder !== 'favorites') {
          await collectionsStore.removeSessionFromFolder(sessionId, fromFolder)
        }

        await collectionsStore.moveSessionToFolder(sessionId, targetId)
        appStore.addNotification({
          type: 'success',
          title: `Session moved to ${targetId}`
        })
      }
    } catch (error) {
      console.error('Error handling drop:', error)
      appStore.addNotification({
        type: 'error',
        title: 'Failed to move session',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      })
    }

    resetDragState()
  }

  const onDragEnd = (event: DragEvent) => {
    const target = event.target as HTMLElement
    target.classList.remove('dragging')

    document.querySelectorAll('.drag-over, .drag-enter').forEach((el) => {
      el.classList.remove('drag-over', 'drag-enter')
    })

    resetDragState()
  }

  const onDragOverItem = (event: DragEvent, targetSession: Session, targetFolder: string) => {
    event.preventDefault()
    event.stopPropagation()

    if (!dragState.isDragging || !dragState.draggedSession) return
    if (dragState.draggedSession.id === targetSession.id) return

    if (dragState.draggedFromFolder !== targetFolder) return

    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
    const midY = rect.top + rect.height / 2
    const isAbove = event.clientY < midY

    const sessions = collectionsStore.sessions.folders?.[targetFolder] || []
    const targetIndex = sessions.findIndex(s => s.id === targetSession.id)

    if (targetIndex !== -1) {
      dragState.dropIndex = isAbove ? targetIndex : targetIndex + 1
      dragState.isReordering = true

      const element = event.currentTarget as HTMLElement
      element.classList.remove('drag-over-top', 'drag-over-bottom')
      element.classList.add(isAbove ? 'drag-over-top' : 'drag-over-bottom')
    }
  }

  const onDragLeaveItem = (event: DragEvent) => {
    const element = event.currentTarget as HTMLElement
    element.classList.remove('drag-over-top', 'drag-over-bottom')
  }

  const onDropItem = async (event: DragEvent, targetSession: Session, targetFolder: string) => {
    event.preventDefault()
    event.stopPropagation()

    const element = event.currentTarget as HTMLElement
    element.classList.remove('drag-over-top', 'drag-over-bottom')

    if (!dragState.isDragging || !dragState.draggedSession) return
    if (dragState.draggedSession.id === targetSession.id) return

    const rect = element.getBoundingClientRect()
    const midY = rect.top + rect.height / 2
    const isAbove = event.clientY < midY

    const sessions = collectionsStore.sessions.folders?.[targetFolder] || []
    const targetIndex = sessions.findIndex(s => s.id === targetSession.id)
    const dropIndex = isAbove ? targetIndex : targetIndex + 1

    // Determine target type based on the actual target folder, not the dragged-from folder
    const targetType: 'favorites' | 'unassigned' | 'folder' =
      targetFolder === 'favorites' ? 'favorites' : targetFolder === 'unassigned' ? 'unassigned' : 'folder'

    await onDrop(event, targetFolder, targetType, dropIndex)
  }

  const resetDragState = () => {
    dragState.isDragging = false
    dragState.draggedSession = null
    dragState.draggedFromFolder = null
    dragState.dropTargetId = null
    dragState.dropTargetType = null
    dragState.dropIndex = null
    dragState.isReordering = false
  }

  return {
    dragState: readonly(dragState),
    onDragStart,
    onDragOver,
    onDragEnter,
    onDragLeave,
    onDrop,
    onDragEnd,
    onDragOverItem,
    onDragLeaveItem,
    onDropItem
  }
}
