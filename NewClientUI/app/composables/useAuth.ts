/**
 * Authentication Composable
 * Manages user authentication, token storage, and auth state
 */

import { useLocalStorage } from '@vueuse/core'
import { AUTH_CONFIG, API_ENDPOINTS } from '~/config/api'
import { useAuthStore } from '~/stores/auth'

export interface User {
  id: string
  email: string
  name?: string
  avatar?: string
  [key: string]: any
}

/**
 * Authentication composable
 */
export function useAuth() {
  const authStore = useAuthStore()

  const token = useLocalStorage<string | null>(AUTH_CONFIG.TOKEN_KEY, null)

  const isAuthenticated = computed(() => authStore.isAuthenticated)
  const user = computed(() => authStore.user)
  const isLoading = computed(() => authStore.isLoading)
  const error = computed(() => authStore.error)

  /**
   * Initialize auth state on app start
   */
  async function initialize() {
    authStore.initialize(token.value, null)
  }

  function setToken(newToken: string) {
    token.value = newToken
    authStore.setToken(newToken)
    authStore.setError(null)
    authStore.setLoading(false)
  }

  /**
   * Clear authentication token
   */
  function clearToken() {
    token.value = null
    authStore.logout()
  }

  /**
   * Handle token from URL (OAuth callback)
   */
  function handleTokenFromUrl() {
    if (import.meta.client) {
      const urlParams = new URLSearchParams(window.location.search)
      const accessToken = urlParams.get('access_token')
      const error = urlParams.get('error')

      if (error) {
        authStore.setError(error)
        window.history.replaceState({}, document.title, window.location.pathname)
        return false
      }

      if (accessToken) {
        setToken(accessToken)
        window.history.replaceState({}, document.title, window.location.pathname)
        return true
      }
    }
    return false
  }

  /**
   * Fetch user information
   */
  async function fetchUser(): Promise<User | null> {
    if (!token.value) {
      authStore.setUser(null)
      return null
    }

    authStore.setLoading(true)
    authStore.setError(null)

    try {
      console.log('Fetching user data from:', API_ENDPOINTS.AUTH.USER)
      const userData = await $fetch<User>(API_ENDPOINTS.AUTH.USER, {
        headers: {
          [AUTH_CONFIG.TOKEN_HEADER]: `${AUTH_CONFIG.TOKEN_PREFIX} ${token.value}`
        }
      })

      authStore.setUser(userData)
      return userData
    } catch (error: unknown) {
      console.error('Failed to fetch user:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch user'

      if ((error as any)?.status === 401) {
        clearToken()
      }

      authStore.setError(errorMessage)
      return null
    } finally {
      authStore.setLoading(false)
    }
  }

  /**
   * Login user (redirect to OAuth provider)
   */
  async function login(): Promise<void> {
    authStore.setLoading(true)
    authStore.setError(null)

    try {
      const response = await $fetch<{ login_url: string }>('/api/query/login-url', {

      })

      if (response.login_url) {
        await redirectToLogin(response.login_url)
      } else {
        throw new Error('Login URL not found')
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed'
      authStore.setError(errorMessage)
    } finally {
      authStore.setLoading(false)
    }
  }

  /**
   * Redirect to login URL
   */
  async function redirectToLogin(loginUrl: string): Promise<void> {
    if (import.meta.client) {
      authStore.setLoading(true)
      window.location.href = loginUrl
    }
  }

  /**
   * Logout user
   */
  async function logout(): Promise<void> {
    authStore.setLoading(true)

    try {
      clearToken()

      if (import.meta.client) {
        localStorage.removeItem('user_email')
        sessionStorage.removeItem('selectedCollection')
      }

      if (import.meta.client) {
        window.location.href = '/'
      }
    } finally {
      authStore.setLoading(false)
    }
  }

  /**
   * Refresh user data
   */
  async function refresh(): Promise<User | null> {
    return await fetchUser()
  }

  if (import.meta.client) {
    handleTokenFromUrl()

    initialize().catch(console.error)
  }

  /**
   * Get current token
   */
  function getToken(): string | null {
    return token.value
  }

  return {
    user: readonly(user),
    isAuthenticated: readonly(isAuthenticated),
    isLoading: readonly(isLoading),
    error: readonly(error),
    getToken,
    setToken,
    handleTokenFromUrl,
    login,
    logout,
    refresh,
    initialize,
    setUser: authStore.setUser
  }
}
