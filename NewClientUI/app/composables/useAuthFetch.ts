/**
 * Authenticated Fetch Composable
 * Wraps around $fetch with automatic token management and error handling
 */

import { AUTH_CONFIG, REQUEST_CONFIG } from '~/config/api'

export interface AuthFetchOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  headers?: Record<string, string>
  body?: any
  timeout?: number
  retry?: number
  retryDelay?: number
}

export interface AuthFetchError extends Error {
  status?: number
  statusText?: string
  data?: any
}

/**
 * Custom fetch wrapper with authentication and error handling
 */
export function useAuthFetch() {
  const { getToken, logout } = useAuth()

  // Prevent recursive calls to handleUnauthorized
  let isHandlingUnauthorized = false

  /**
   * Authenticated fetch function
   */
  async function authFetch<T = any>(
    url: string,
    options: AuthFetchOptions = {}
  ): Promise<T> {
    const {
      timeout = REQUEST_CONFIG.TIMEOUT,
      retry = REQUEST_CONFIG.RETRY_ATTEMPTS,
      retryDelay = REQUEST_CONFIG.RETRY_DELAY,
      headers = {},
      ...fetchOptions
    } = options

    // Add authentication header if token exists
    const token = getToken()
    const authHeaders: Record<string, string> = {}

    if (token) {
      authHeaders[AUTH_CONFIG.TOKEN_HEADER] = `${AUTH_CONFIG.TOKEN_PREFIX} ${token}`
    }

    const requestOptions = {
      ...fetchOptions,
      headers: {
        'Content-Type': 'application/json',
        ...authHeaders,
        ...headers
      }
    }

    let lastError: AuthFetchError | null = null

    // Retry logic
    for (let attempt = 0; attempt <= retry; attempt++) {
      try {
        const response = await $fetch<T>(url, {
          ...requestOptions,
          timeout,
          onResponseError({ response }) {
            // Handle 401 Unauthorized
            if (response.status === 401) {
              handleUnauthorized()
              throw new Error('Unauthorized')
            }
          }
        })

        return response
      } catch (error: any) {
        lastError = error

        // Don't retry on 401 or 403
        if (error.status === 401 || error.status === 403) {
          break
        }

        // Don't retry on last attempt
        if (attempt === retry) {
          break
        }

        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, retryDelay))
      }
    }

    // Throw the last error if all retries failed
    throw lastError || new Error('Request failed')
  }

  /**
   * Handle unauthorized responses - immediate logout and redirect
   */
  async function handleUnauthorized() {
    // Prevent recursive calls
    if (isHandlingUnauthorized) {
      return
    }

    isHandlingUnauthorized = true

    try {
      // Immediately clear token and logout
      await logout()

      // Redirect to home page (which will trigger login)
      if (import.meta.client) {
        window.location.href = '/'
      }
    } catch (error) {
      console.error('Error during logout:', error)
      // Force redirect even if logout fails
      if (import.meta.client) {
        window.location.href = '/'
      }
    } finally {
      isHandlingUnauthorized = false
    }
  }

  /**
   * GET request
   */
  async function get<T = any>(url: string, options?: Omit<AuthFetchOptions, 'method'>): Promise<T> {
    return authFetch<T>(url, { ...options, method: 'GET' })
  }

  /**
   * POST request
   */
  async function post<T = any>(
    url: string,
    body?: any,
    options?: Omit<AuthFetchOptions, 'method' | 'body'>
  ): Promise<T> {
    return authFetch<T>(url, {
      ...options,
      method: 'POST',
      body: body ? JSON.stringify(body) : undefined
    })
  }

  /**
   * PUT request
   */
  async function put<T = any>(
    url: string,
    body?: any,
    options?: Omit<AuthFetchOptions, 'method' | 'body'>
  ): Promise<T> {
    return authFetch<T>(url, {
      ...options,
      method: 'PUT',
      body: body ? JSON.stringify(body) : undefined
    })
  }

  /**
   * DELETE request
   */
  async function del<T = any>(url: string, options?: Omit<AuthFetchOptions, 'method'>): Promise<T> {
    return authFetch<T>(url, { ...options, method: 'DELETE' })
  }

  /**
   * PATCH request
   */
  async function patch<T = any>(
    url: string,
    body?: any,
    options?: Omit<AuthFetchOptions, 'method' | 'body'>
  ): Promise<T> {
    return authFetch<T>(url, {
      ...options,
      method: 'PATCH',
      body: body ? JSON.stringify(body) : undefined
    })
  }

  /**
   * Upload files with FormData
   */
  async function upload<T = any>(
    url: string,
    formData: FormData,
    options?: Omit<AuthFetchOptions, 'method' | 'body'>
  ): Promise<T> {
    const token = getToken()
    const authHeaders: Record<string, string> = {}

    if (token) {
      authHeaders[AUTH_CONFIG.TOKEN_HEADER] = `${AUTH_CONFIG.TOKEN_PREFIX} ${token}`
    }

    return authFetch<T>(url, {
      ...options,
      method: 'POST',
      body: formData,
      headers: {
        // Don't set Content-Type for FormData, let browser set it with boundary
        ...authHeaders,
        ...(options?.headers || {})
      }
    })
  }

  /**
   * Raw fetch with authentication for streaming responses
   */
  async function fetchWithAuth(
    url: string,
    options: RequestInit = {}
  ): Promise<Response> {
    const token = getToken()
    const authHeaders: Record<string, string> = {}

    if (token) {
      authHeaders[AUTH_CONFIG.TOKEN_HEADER] = `${AUTH_CONFIG.TOKEN_PREFIX} ${token}`
    }

    // Use native fetch for streaming support
    const response = await fetch(url, {
      ...options,
      headers: {
        ...authHeaders,
        ...(options.headers || {})
      }
    })

    // Handle 401 Unauthorized - removed duplicate handling
    // This is now handled by the onResponseError in authFetch
    if (response.status === 401) {
      handleUnauthorized()
      throw new Error('Unauthorized')
    }

    return response
  }

  return {
    fetchWithAuth,
    get,
    post,
    put,
    delete: del,
    patch,
    upload
  }
}
