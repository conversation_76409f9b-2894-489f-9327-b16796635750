// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  // Enable SPA mode - no server-side rendering

  modules: [
    '@nuxt/eslint',
    '@nuxt/ui',
    '@nuxtjs/mdc',
    '@nuxthub/core',
    '@pinia/nuxt',
    'nuxt-auth-utils'
  ],
  ssr: false,

  devtools: {
    enabled: false
  },

  // SEO optimization for SPA
  app: {
    head: {
      title: 'fbeta - AI Assistant',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { name: 'description', content: 'AI-powered assistant for domain-specific conversations and document analysis' },
        { name: 'keywords', content: 'AI, assistant, chat, document analysis, fbeta' },
        { property: 'og:title', content: 'fbeta - AI Assistant' },
        { property: 'og:description', content: 'AI-powered assistant for domain-specific conversations' },
        { property: 'og:type', content: 'website' }
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }
      ]
    }
  },

  css: ['~/assets/css/main.css'],

  mdc: {
    highlight: {
      // noApiRoute: true
      shikiEngine: 'javascript'
    }
  },

  devServer: {
    port: 5179,
    host: '0.0.0.0'
  },

  future: {
    compatibilityVersion: 4
  },

  experimental: {
    viewTransition: true
  },

  compatibilityDate: '2024-07-11',

  nitro: {
    experimental: {
      openAPI: true
    },
    devProxy: {
      '/api/query': {
        target: process.env.QUERY_BASE_URL || 'http://localhost:8003',
        changeOrigin: true,
        prependPath: true
      },
      '/api/loader': {
        target: process.env.LOADER_BASE_URL || 'http://localhost:8002',
        changeOrigin: true,
        prependPath: true
      },
      '/slash-commands': {
        target: process.env.QUERY_BASE_URL || 'http://localhost:8003',
        changeOrigin: true,
        prependPath: true
      }
    }
  },

  hub: {
    ai: true,
    database: false
  },

  vite: {
    optimizeDeps: {
      include: ['debug']
    },

    $server: {
      build: {
        rollupOptions: {
          output: {
            preserveModules: true
          }
        }
      }
    }
  },

  eslint: {
    config: {
      stylistic: {
        commaDangle: 'never',
        braceStyle: '1tbs'
      }
    }
  }
})
